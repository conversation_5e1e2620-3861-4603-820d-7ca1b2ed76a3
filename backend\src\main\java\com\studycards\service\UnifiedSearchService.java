package com.studycards.service;

import com.studycards.dto.*;
import com.studycards.model.Deck;
import com.studycards.model.Card;
import com.studycards.model.User;
import com.studycards.repository.DeckRepository;
import com.studycards.repository.CardRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Unified search service that consolidates all search functionality
 * with performance optimizations and relevance ranking
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class UnifiedSearchService {

    @Autowired
    private DeckRepository deckRepository;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private DeckService deckService;

    @Autowired
    private SearchAnalyticsService searchAnalyticsService;

    @Value("${app.search.max-results:1000}")
    private int maxSearchResults;

    @Value("${app.search.enable-relevance-ranking:true}")
    private boolean enableRelevanceRanking;

    /**
     * Unified deck search with consolidated logic and database-level filtering
     */
    public Page<DeckResponse> searchDecks(DeckSearchRequest searchRequest) {
        log.debug("Performing unified deck search with request: {}", searchRequest);
        
        // Validate and sanitize request
        validateSearchRequest(searchRequest);
        
        // Track search analytics
        trackSearchRequest(searchRequest, "deck");
        
        // Get current user for security filtering
        User currentUser = userService.getCurrentUser();
        
        // Create pageable with enhanced sorting
        Pageable pageable = createPageable(searchRequest);
        
        // Execute unified search query
        Page<Deck> decks = executeUnifiedDeckSearch(searchRequest, currentUser, pageable);
        
        // Apply relevance ranking if enabled
        if (shouldApplyRelevanceRanking(searchRequest)) {
            decks = applyRelevanceRanking(decks, searchRequest);
        }
        
        // Convert to response DTOs with batch optimization
        return decks.map(deck -> deckService.mapToDeckResponse(deck, currentUser));
    }

    /**
     * Unified card search with consolidated logic
     */
    public Page<CardResponse> searchCards(CardSearchRequest searchRequest) {
        log.debug("Performing unified card search with request: {}", searchRequest);
        
        // Validate and sanitize request
        validateCardSearchRequest(searchRequest);
        
        // Track search analytics
        trackSearchRequest(searchRequest, "card");
        
        // Get current user for security filtering
        User currentUser = userService.getCurrentUser();
        
        // Create pageable with enhanced sorting
        Pageable pageable = createCardPageable(searchRequest);
        
        // Execute unified search query
        Page<Card> cards = executeUnifiedCardSearch(searchRequest, currentUser, pageable);
        
        // Convert to response DTOs
        return cards.map(card -> mapToCardResponse(card, currentUser));
    }

    /**
     * Execute unified deck search with database-level filtering
     */
    private Page<Deck> executeUnifiedDeckSearch(DeckSearchRequest request, User currentUser, Pageable pageable) {
        // Use the new unified repository method with database-level subscription filtering
        List<String> tagNames = request.getTagNames();
        boolean tagNamesEmpty = tagNames == null || tagNames.isEmpty();

        return deckRepository.unifiedAdvancedSearch(
            request.getQuery(),
            request.getIsPublic(),
            request.getCreatorId(),
            tagNames,
            tagNamesEmpty,
            request.getMinDifficulty(),
            request.getMaxDifficulty(),
            request.getMinCardCount(),
            request.getMaxCardCount(),
            request.getIncludeDeleted() != null ? request.getIncludeDeleted() : false,
            request.getIncludeFolders(),
            request.getParentFolderId(),
            request.getIsCollaborative(),
            request.getCreatedAfter(),
            request.getCreatedBefore(),
            request.getUpdatedAfter(),
            request.getUpdatedBefore(),
            request.getFavoritesOnly() != null ? request.getFavoritesOnly() : false,
            request.getIncludeExpiredCreators() != null ? request.getIncludeExpiredCreators() : false,
            currentUser.getId(),
            pageable
        );
    }

    /**
     * Execute unified card search
     */
    private Page<Card> executeUnifiedCardSearch(CardSearchRequest request, User currentUser, Pageable pageable) {
        List<Long> deckIds = request.getDeckIds();
        boolean deckIdsEmpty = deckIds == null || deckIds.isEmpty();
        List<String> tagNames = request.getTagNames();
        boolean tagNamesEmpty = tagNames == null || tagNames.isEmpty();

        return cardRepository.unifiedAdvancedSearch(
            request.getQuery(),
            deckIds,
            deckIdsEmpty,
            tagNames,
            tagNamesEmpty,
            request.getMinDifficulty(),
            request.getMaxDifficulty(),
            request.getMinProgress(),
            request.getMaxProgress(),
            request.getCreatedAfter(),
            request.getCreatedBefore(),
            request.getUpdatedAfter(),
            request.getUpdatedBefore(),
            request.getReviewDateAfter(),
            request.getReviewDateBefore(),
            request.getIncludeCollaborative() != null ? request.getIncludeCollaborative() : true,
            request.getIncludePublicDecks() != null ? request.getIncludePublicDecks() : true,
            request.getIncludePrivateDecks() != null ? request.getIncludePrivateDecks() : true,
            request.getIncludeDueCards() != null ? request.getIncludeDueCards() : false,
            currentUser.getId(),
            pageable
        );
    }

    /**
     * Create enhanced pageable with relevance-based sorting
     */
    private Pageable createPageable(DeckSearchRequest request) {
        // Limit page size to prevent memory issues
        int size = Math.min(request.getSize(), maxSearchResults);

        Sort sort;
        if (shouldApplyRelevanceRanking(request)) {
            // Use relevance ranking - for now, just use createdAt desc as relevance proxy
            // TODO: Implement proper relevance scoring in the future
            sort = Sort.by(Sort.Direction.DESC, "createdAt");
        } else {
            // Use standard sorting
            Sort.Direction direction = "asc".equalsIgnoreCase(request.getDirection())
                ? Sort.Direction.ASC : Sort.Direction.DESC;
            sort = Sort.by(direction, request.getSortBy());
        }

        return PageRequest.of(request.getPage(), size, sort);
    }

    /**
     * Create pageable for card search
     */
    private Pageable createCardPageable(CardSearchRequest request) {
        int size = Math.min(request.getSize(), maxSearchResults);
        Sort.Direction direction = "asc".equalsIgnoreCase(request.getDirection()) 
            ? Sort.Direction.ASC : Sort.Direction.DESC;
        Sort sort = Sort.by(direction, request.getSortBy());
        return PageRequest.of(request.getPage(), size, sort);
    }

    /**
     * Validate search request
     */
    private void validateSearchRequest(DeckSearchRequest request) {
        if (request.getMaxResults() != null && request.getMaxResults() > maxSearchResults) {
            request.setMaxResults(maxSearchResults);
        }
        
        // Sanitize query
        if (request.getQuery() != null) {
            request.setQuery(request.getQuery().trim());
            if (request.getQuery().length() > 500) {
                request.setQuery(request.getQuery().substring(0, 500));
            }
        }
    }

    /**
     * Validate card search request
     */
    private void validateCardSearchRequest(CardSearchRequest request) {
        if (request.getQuery() != null) {
            request.setQuery(request.getQuery().trim());
            if (request.getQuery().length() > 500) {
                request.setQuery(request.getQuery().substring(0, 500));
            }
        }
    }

    /**
     * Check if relevance ranking should be applied
     */
    private boolean shouldApplyRelevanceRanking(DeckSearchRequest request) {
        return enableRelevanceRanking && 
               request.getEnableRelevanceRanking() != null && 
               request.getEnableRelevanceRanking() &&
               request.getQuery() != null && 
               !request.getQuery().trim().isEmpty();
    }

    /**
     * Apply relevance ranking to search results
     */
    private Page<Deck> applyRelevanceRanking(Page<Deck> decks, DeckSearchRequest request) {
        // This would be implemented with a more sophisticated ranking algorithm
        // For now, we'll rely on the database query ordering
        return decks;
    }

    /**
     * Track search request for analytics
     */
    private void trackSearchRequest(Object request, String searchType) {
        try {
            searchAnalyticsService.trackSearch(request, searchType);
        } catch (Exception e) {
            log.warn("Failed to track search analytics: {}", e.getMessage());
        }
    }

    /**
     * Map card to response DTO
     */
    private CardResponse mapToCardResponse(Card card, User currentUser) {
        return CardResponse.builder()
            .id(card.getId())
            .question(card.getQuestion())
            .answer(card.getAnswer())
            .notes(card.getNotes())
            .hint(card.getHint())
            .deckId(card.getDeck().getId())
            .deckTitle(card.getDeck().getTitle())


            .difficultyLevel(card.getDifficultyLevel())
            .learningProgress(card.getLearningProgress())
            .nextReviewDate(card.getNextReviewDate())
            .createdAt(card.getCreatedAt())
            .updatedAt(card.getUpdatedAt())
            .tags(card.getCardTags() != null ?
                card.getCardTags().stream()
                    .map(tag -> tag.getTagName())
                    .collect(Collectors.toList()) :
                Collections.emptyList())


            .build();
    }
}
