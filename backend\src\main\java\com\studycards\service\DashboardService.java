package com.studycards.service;

import com.studycards.dto.*;
import com.studycards.model.Card;
import com.studycards.model.Deck;
import com.studycards.model.StudySession;
import com.studycards.model.User;
import com.studycards.repository.CardRepository;
import com.studycards.repository.DeckRepository;
import com.studycards.repository.StudySessionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.sql.Date;

@Service
@Slf4j
public class DashboardService {

    @Autowired
    private UserService userService;

    @Autowired
    private DeckRepository deckRepository;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private StudySessionRepository studySessionRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ContentVisibilityService contentVisibilityService;

    /**
     * Get comprehensive dashboard data for the current user with enhanced error handling
     * STRICT ENFORCEMENT: Users with expired subscriptions cannot access their own dashboard
     *
     * @return Dashboard data
     */
    public DashboardResponse getDashboardData() {
        try {
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                log.error("Current user is null when getting dashboard data");
                throw new IllegalStateException("User not authenticated");
            }

            // STRICT ENFORCEMENT: Block expired users from accessing their own dashboard
            if (!contentVisibilityService.canAccessOwnContent(currentUser)) {
                // Return empty dashboard for expired users
                return DashboardResponse.builder()
                        .streak(0)
                        .cardsStudiedToday(0)
                        .totalCardsStudied(0)
                        .averageAccuracy(0.0)
                        .dueCards(0)
                        .totalDecks(0)
                        .recentSessionsCount(0)
                        .deckProgress(Collections.emptyList())
                        .recentDecks(Collections.emptyList())
                        .dailyGoal(0)
                        .studyTimeToday(0)
                        .dailyTimeGoal(0)
                        .upcomingReviews(Collections.emptyList())
                        .build();
            }

            LocalDate today = LocalDate.now();
            LocalDateTime startOfToday = today.atStartOfDay();
            LocalDateTime endOfToday = today.plusDays(1).atStartOfDay().minusNanos(1);

            log.debug("Getting dashboard data for user: {} on date: {}", currentUser.getUsername(), today);

            // Get user's decks with error handling
            List<Deck> userDecks;
            try {
                userDecks = deckRepository.findByCreatorAndDeletedFalse(currentUser);
                if (userDecks == null) {
                    userDecks = List.of();
                }
            } catch (Exception e) {
                log.error("Error fetching user decks for dashboard: {}", e.getMessage(), e);
                userDecks = List.of();
            }

            // Get study streak with error handling
            int streak;
            try {
                streak = calculateStudyStreak(currentUser);
            } catch (Exception e) {
                log.error("Error calculating study streak: {}", e.getMessage(), e);
                streak = 0;
            }

            // Get cards studied today with error handling
            int cardsStudiedToday;
            try {
                List<StudySession> todaySessions = studySessionRepository.findByUserAndDateRange(
                        currentUser, startOfToday, endOfToday);
                cardsStudiedToday = todaySessions != null ? todaySessions.stream()
                        .mapToInt(StudySession::getCardsStudied)
                        .sum() : 0;
            } catch (Exception e) {
                log.error("Error fetching today's study sessions: {}", e.getMessage(), e);
                cardsStudiedToday = 0;
            }

            // Get total cards studied with error handling
            Long totalCardsStudied;
            try {
                totalCardsStudied = studySessionRepository.countTotalCardsStudiedByUser(currentUser);
                if (totalCardsStudied == null) {
                    totalCardsStudied = 0L;
                }
            } catch (Exception e) {
                log.error("Error fetching total cards studied: {}", e.getMessage(), e);
                totalCardsStudied = 0L;
            }

            // Get average accuracy with error handling
            Double averageAccuracy;
            try {
                averageAccuracy = studySessionRepository.getAverageAccuracyByUser(currentUser);
                if (averageAccuracy == null) {
                    averageAccuracy = 0.0;
                }
            } catch (Exception e) {
                log.error("Error fetching average accuracy: {}", e.getMessage(), e);
                averageAccuracy = 0.0;
            }

            // Get cards due for review with error handling
            int dueCardsCount;
            try {
                List<Card> dueCards = cardRepository.findAllDueCardsForUser(today, currentUser.getId());
                dueCardsCount = dueCards != null ? dueCards.size() : 0;
            } catch (Exception e) {
                log.error("Error fetching due cards: {}", e.getMessage(), e);
                dueCardsCount = 0;
            }

            // Get recent sessions with error handling
            List<StudySession> recentSessions;
            try {
                recentSessions = studySessionRepository.findByUserAndStartTimeBetween(
                        currentUser,
                        LocalDateTime.now().minusDays(7),
                        LocalDateTime.now());
                if (recentSessions == null) {
                    recentSessions = List.of();
                }
            } catch (Exception e) {
                log.error("Error fetching recent sessions: {}", e.getMessage(), e);
                recentSessions = List.of();
            }

            // Create notifications if needed (with error handling)
            try {
                if (dueCardsCount > 0) {
                    notificationService.createDueCardsReminder(currentUser, dueCardsCount);
                }

                if (streak >= 3) {
                    notificationService.createStudyStreakNotification(currentUser, streak);
                }
            } catch (Exception e) {
                log.warn("Error creating notifications: {}", e.getMessage(), e);
                // Don't fail the dashboard request if notifications fail
            }

            // Get deck progress data with error handling
            List<DashboardResponse.DeckProgress> deckProgressList;
            try {
                deckProgressList = calculateDeckProgress(currentUser, userDecks);
            } catch (Exception e) {
                log.error("Error calculating deck progress: {}", e.getMessage(), e);
                deckProgressList = List.of();
            }

            // Get recent decks with error handling
            List<DashboardResponse.RecentDeck> recentDecksList;
            try {
                recentDecksList = getRecentDecks(currentUser);
            } catch (Exception e) {
                log.error("Error fetching recent decks: {}", e.getMessage(), e);
                recentDecksList = List.of();
            }

            // Get upcoming reviews with error handling
            List<DashboardResponse.UpcomingReview> upcomingReviewsList;
            try {
                upcomingReviewsList = getUpcomingReviews(currentUser);
            } catch (Exception e) {
                log.error("Error fetching upcoming reviews: {}", e.getMessage(), e);
                upcomingReviewsList = List.of();
            }

            // Calculate study time today with error handling
            int studyTimeToday;
            try {
                List<StudySession> todaySessions = studySessionRepository.findByUserAndDateRange(
                        currentUser, startOfToday, endOfToday);
                studyTimeToday = todaySessions != null ? todaySessions.stream()
                        .mapToInt(session -> (int) session.getDurationInMinutes())
                        .sum() : 0;
            } catch (Exception e) {
                log.error("Error calculating study time today: {}", e.getMessage(), e);
                studyTimeToday = 0;
            }

            // Default goals (in a real app, these would be user-configurable)
            int dailyGoal = 20; // cards per day
            int dailyTimeGoal = 30; // minutes per day

            log.debug("Dashboard data calculated successfully for user: {}", currentUser.getUsername());

            // Calculate total cards across all decks
            int totalCards = userDecks.stream()
                    .mapToInt(deck -> cardRepository.countByDeckId(deck.getId()))
                    .sum();

            // Build the response
            return DashboardResponse.builder()
                    .streak(streak)
                    .cardsStudiedToday(cardsStudiedToday)
                    .totalCardsStudied(totalCardsStudied.intValue())
                    .totalCards(totalCards)
                    .averageAccuracy(Math.round(averageAccuracy * 10) / 10.0) // Round to 1 decimal place
                    .dueCards(dueCardsCount)
                    .totalDecks(userDecks.size())
                    .recentSessionsCount(recentSessions.size())
                    .deckProgress(deckProgressList)
                    .recentDecks(recentDecksList)
                    .dailyGoal(dailyGoal)
                    .studyTimeToday(studyTimeToday)
                    .dailyTimeGoal(dailyTimeGoal)
                    .upcomingReviews(upcomingReviewsList)
                    .build();

        } catch (Exception e) {
            log.error("Unexpected error in getDashboardData: {}", e.getMessage(), e);
            // Return a minimal dashboard response instead of failing completely
            return DashboardResponse.builder()
                    .streak(0)
                    .cardsStudiedToday(0)
                    .totalCardsStudied(0)
                    .totalCards(0)
                    .averageAccuracy(0.0)
                    .dueCards(0)
                    .totalDecks(0)
                    .recentSessionsCount(0)
                    .deckProgress(List.of())
                    .recentDecks(List.of())
                    .dailyGoal(20)
                    .studyTimeToday(0)
                    .dailyTimeGoal(30)
                    .upcomingReviews(List.of())
                    .build();
        }
    }

    /**
     * Get study activity data for the specified number of days
     *
     * @param days Number of days to include
     * @return Study activity data
     */
    public StudyActivityResponse getStudyActivityData(int days) {
        User currentUser = userService.getCurrentUser();
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);

        // Get sessions in the date range
        List<StudySession> sessions = studySessionRepository.findByUserAndDateRange(
                currentUser, startDate, endDate);

        // Group sessions by date
        Map<LocalDate, List<StudySession>> sessionsByDate = sessions.stream()
                .collect(Collectors.groupingBy(session -> session.getStartTime().toLocalDate()));

        // Create activity data for each day
        List<StudyActivityResponse.DailyActivity> dailyActivities = new ArrayList<>();

        LocalDate currentDate = startDate.toLocalDate();
        while (!currentDate.isAfter(endDate.toLocalDate())) {
            List<StudySession> dailySessions = sessionsByDate.getOrDefault(currentDate, Collections.emptyList());

            int cardsStudied = dailySessions.stream()
                    .mapToInt(StudySession::getCardsStudied)
                    .sum();

            double accuracy = dailySessions.stream()
                    .filter(s -> s.getCardsStudied() > 0)
                    .mapToDouble(s -> (double) s.getCorrectAnswers() / s.getCardsStudied() * 100)
                    .average()
                    .orElse(0.0);

            dailyActivities.add(StudyActivityResponse.DailyActivity.builder()
                    .date(currentDate)
                    .cardsStudied(cardsStudied)
                    .accuracy(Math.round(accuracy * 10) / 10.0) // Round to 1 decimal place
                    .sessionsCount(dailySessions.size())
                    .build());

            currentDate = currentDate.plusDays(1);
        }

        return StudyActivityResponse.builder()
                .activities(dailyActivities)
                .build();
    }

    /**
     * Get daily goal progress
     *
     * @return Daily goal progress
     */
    public DailyGoalResponse getDailyGoalProgress() {
        User currentUser = userService.getCurrentUser();
        LocalDate today = LocalDate.now();
        LocalDateTime startOfToday = today.atStartOfDay();
        LocalDateTime endOfToday = today.plusDays(1).atStartOfDay().minusNanos(1);

        // Get today's sessions
        List<StudySession> todaySessions = studySessionRepository.findByUserAndDateRange(
                currentUser, startOfToday, endOfToday);

        // Calculate cards studied today
        int cardsStudiedToday = todaySessions.stream()
                .mapToInt(StudySession::getCardsStudied)
                .sum();

        // Default daily goal (in a real app, this would be user-configurable)
        int dailyGoal = 20;

        // Calculate progress percentage
        int progressPercentage = Math.min(100, (int) Math.round((double) cardsStudiedToday / dailyGoal * 100));

        return DailyGoalResponse.builder()
                .cardsStudiedToday(cardsStudiedToday)
                .dailyGoal(dailyGoal)
                .progressPercentage(progressPercentage)
                .build();
    }

    /**
     * Get cards due for review
     * STRICT ENFORCEMENT: Users with expired subscriptions cannot access their own cards
     *
     * @return Cards due for review
     */
    public ReviewCardsResponse getCardsForReview() {
        User currentUser = userService.getCurrentUser();

        // STRICT ENFORCEMENT: Block expired users from accessing their own content
        if (!contentVisibilityService.canAccessOwnContent(currentUser)) {
            // Return empty response for expired users
            return ReviewCardsResponse.builder()
                    .cards(Collections.emptyList())
                    .totalDueCards(0)
                    .overdueCards(0)
                    .newCards(0)
                    .reviewCards(0)
                    .deckCount(0)
                    .metadata(ReviewCardsResponse.ReviewSessionMetadata.builder()
                            .totalDueCards(0)
                            .returnedCards(0)
                            .overdueCards(0)
                            .nextDueDate(null)
                            .difficultyDistribution(Collections.emptyMap())
                            .estimatedMinutesToComplete(0)
                            .build())
                    .build();
        }

        LocalDate today = LocalDate.now();

        // Get all due cards for the user
        List<Card> dueCards = cardRepository.findAllDueCardsForUser(today, currentUser.getId());

        // Group cards by deck
        Map<Long, List<Card>> cardsByDeck = dueCards.stream()
                .collect(Collectors.groupingBy(card -> card.getDeck().getId()));

        // Calculate metadata
        int totalDueCards = dueCards.size();

        // Count overdue cards (due before today)
        int overdueCards = (int) dueCards.stream()
                .filter(card -> card.getNextReviewDate().isBefore(today))
                .count();

        // Calculate difficulty distribution
        Map<Integer, Integer> difficultyDistribution = dueCards.stream()
                .collect(Collectors.groupingBy(
                        Card::getDifficultyLevel,
                        Collectors.summingInt(card -> 1)
                ));

        // Find next due date after today
        LocalDate nextDueDate = null;
        for (Long deckId : cardsByDeck.keySet()) {
            LocalDate deckNextDueDate = cardRepository.findNextDueDateForDeck(deckId, today);
            if (deckNextDueDate != null) {
                if (nextDueDate == null || deckNextDueDate.isBefore(nextDueDate)) {
                    nextDueDate = deckNextDueDate;
                }
            }
        }

        // Estimate time to complete (2 minutes per card on average)
        int estimatedMinutes = dueCards.size() * 2;

        // Create metadata
        ReviewCardsResponse.ReviewSessionMetadata metadata = ReviewCardsResponse.ReviewSessionMetadata.builder()
                .totalDueCards(totalDueCards)
                .returnedCards(dueCards.size())
                .overdueCards(overdueCards)
                .nextDueDate(nextDueDate)
                .difficultyDistribution(difficultyDistribution)
                .estimatedMinutesToComplete(estimatedMinutes)
                .build();

        // Map cards to DTOs
        List<CardResponse> cardResponses = dueCards.stream()
                .map(this::mapToCardResponse)
                .collect(Collectors.toList());

        return ReviewCardsResponse.builder()
                .cards(cardResponses)
                .metadata(metadata)
                .totalDueCards(totalDueCards)
                .overdueCards(overdueCards)
                .newCards(0) // TODO: Implement new cards logic
                .reviewCards(totalDueCards - overdueCards)
                .deckCount(cardsByDeck.size())
                .build();
    }

    /**
     * Calculate deck progress for the user's decks
     *
     * @param user The user
     * @param userDecks List of user's decks
     * @return List of deck progress data
     */
    private List<DashboardResponse.DeckProgress> calculateDeckProgress(User user, List<Deck> userDecks) {
        List<DashboardResponse.DeckProgress> progressList = new ArrayList<>();

        for (Deck deck : userDecks) {
            try {
                // Get study statistics for this deck
                Map<String, Object> stats = studySessionRepository.getDeckStudyStatistics(user, deck);

                // Get total cards in deck
                int totalCards = cardRepository.countByDeckId(deck.getId());

                // Calculate cards learned (cards that have been studied at least once)
                int cardsLearned = 0;
                int studySessionCount = 0;

                if (stats != null && !stats.isEmpty()) {
                    // Get total cards studied (this might be more than unique cards if cards are studied multiple times)
                    int totalCardsStudied = ((Number) stats.getOrDefault("totalCardsStudied", 0)).intValue();
                    studySessionCount = ((Number) stats.getOrDefault("sessionCount", 0)).intValue();

                    // Estimate unique cards learned (conservative estimate)
                    cardsLearned = Math.min(totalCardsStudied, totalCards);
                }

                // Calculate progress percentage
                double progressPercentage = totalCards > 0 ? (double) cardsLearned / totalCards * 100 : 0.0;

                DashboardResponse.DeckProgress progress = DashboardResponse.DeckProgress.builder()
                        .deckId(deck.getId())
                        .deckTitle(deck.getTitle())
                        .cardsLearned(cardsLearned)
                        .totalCards(totalCards)
                        .progressPercentage(Math.round(progressPercentage * 10) / 10.0) // Round to 1 decimal place
                        .studySessionCount(studySessionCount)
                        .build();

                progressList.add(progress);
            } catch (Exception e) {
                log.warn("Error calculating progress for deck {}: {}", deck.getId(), e.getMessage());
                // Add a default progress entry for this deck
                DashboardResponse.DeckProgress progress = DashboardResponse.DeckProgress.builder()
                        .deckId(deck.getId())
                        .deckTitle(deck.getTitle())
                        .cardsLearned(0)
                        .totalCards(cardRepository.countByDeckId(deck.getId()))
                        .progressPercentage(0.0)
                        .studySessionCount(0)
                        .build();
                progressList.add(progress);
            }
        }

        return progressList;
    }

    /**
     * Calculate the current study streak for a user
     *
     * @param user The user
     * @return Current streak in days
     */
    private int calculateStudyStreak(User user) {
        // Get distinct study dates in descending order
        List<Date> studyDates = studySessionRepository.getDistinctStudyDatesByUser(user.getId());

        log.debug("Calculating streak for user {}: found {} distinct study dates", user.getUsername(), studyDates.size());

        if (studyDates.isEmpty()) {
            log.debug("No study dates found for user {}, returning streak 0", user.getUsername());
            return 0;
        }

        // Convert Date objects to LocalDate and sort in descending order
        List<LocalDate> sortedDates = studyDates.stream()
                .map(date -> date.toLocalDate())
                .sorted((d1, d2) -> d2.compareTo(d1)) // Descending order
                .collect(Collectors.toList());

        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);

        log.debug("Today: {}, Yesterday: {}, Most recent study date: {}", today, yesterday, sortedDates.get(0));

        // Check if user studied today or yesterday to maintain streak
        boolean studiedRecently = sortedDates.get(0).equals(today) || sortedDates.get(0).equals(yesterday);

        if (!studiedRecently) {
            log.debug("User {} hasn't studied recently (most recent: {}), streak broken", user.getUsername(), sortedDates.get(0));
            return 0; // Streak broken
        }

        // Count consecutive days
        int streak = 1; // Start with 1 for today/yesterday
        LocalDate expectedDate = sortedDates.get(0).minusDays(1);

        for (int i = 1; i < sortedDates.size(); i++) {
            LocalDate currentDate = sortedDates.get(i);

            if (currentDate.equals(expectedDate)) {
                streak++;
                expectedDate = expectedDate.minusDays(1);
            } else if (currentDate.isBefore(expectedDate)) {
                // Gap in streak
                log.debug("Gap found in streak at date {}, expected {}", currentDate, expectedDate);
                break;
            }
        }

        log.debug("Calculated streak for user {}: {} days", user.getUsername(), streak);
        return streak;
    }

    /**
     * Map a Card entity to a CardResponse DTO
     *
     * @param card The card entity
     * @return CardResponse DTO
     */
    private CardResponse mapToCardResponse(Card card) {
        return CardResponse.builder()
                .id(card.getId())
                .question(card.getQuestion())
                .answer(card.getAnswer())
                .difficultyLevel(card.getDifficultyLevel())
                .questionType(card.getQuestionType())
                .nextReviewDate(card.getNextReviewDate())
                .deckId(card.getDeck().getId())
                .deckTitle(card.getDeck().getTitle())
                .createdAt(card.getCreatedAt())
                .updatedAt(card.getUpdatedAt())
                // Image URLs removed as per user requirement
                .hint(card.getHint())
                .notes(card.getNotes())
                .reviewCount(card.getReviewCount())
                .correctCount(card.getCorrectCount())
                .learningProgress(card.getLearningProgress())
                .intervalDays(card.getIntervalDays())
                .easeFactor(card.getEaseFactor().floatValue())
                .build();
    }

    /**
     * Scheduled task to generate daily notifications for all users
     * Runs at 8:00 AM every day
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void generateDailyNotifications() {
        log.info("Generating daily notifications for all users");
        LocalDate today = LocalDate.now();

        // Get all users
        List<User> users = userService.getAllUsers();

        for (User user : users) {
            try {
                // Get due cards for the user
                List<Card> dueCards = cardRepository.findAllDueCardsForUser(today, user.getId());
                int dueCardsCount = dueCards.size();

                // Create due cards notification if needed
                if (dueCardsCount > 0) {
                    notificationService.createDueCardsReminder(user, dueCardsCount);
                }

                // Calculate streak for the user
                int streak = calculateStudyStreak(user);

                // Create streak notification if needed
                if (streak > 0 && streak % 5 == 0) { // Notify on 5, 10, 15, etc. days
                    notificationService.createStudyStreakNotification(user, streak);
                }
            } catch (Exception e) {
                log.error("Error generating notifications for user {}: {}", user.getUsername(), e.getMessage());
            }
        }

        log.info("Finished generating daily notifications");
    }

    /**
     * Get upcoming reviews for the user
     *
     * @param currentUser The current user
     * @return List of upcoming reviews
     */
    private List<DashboardResponse.UpcomingReview> getUpcomingReviews(User currentUser) {
        try {
            LocalDate today = LocalDate.now();
            LocalDate tomorrow = today.plusDays(1);

            // Get cards due in the next 24 hours
            List<Card> upcomingCards = cardRepository.findByNextReviewDateBetween(today, tomorrow);

            // Group by deck and create upcoming review items
            Map<Deck, List<Card>> cardsByDeck = upcomingCards.stream()
                    .filter(card -> card.getDeck().getCreator().equals(currentUser))
                    .collect(Collectors.groupingBy(Card::getDeck));

            return cardsByDeck.entrySet().stream()
                    .map(entry -> {
                        Deck deck = entry.getKey();
                        List<Card> cards = entry.getValue();

                        // Find the earliest due time for this deck
                        LocalDate earliestDueDate = cards.stream()
                                .map(Card::getNextReviewDate)
                                .min(LocalDate::compareTo)
                                .orElse(LocalDate.now());

                        LocalDateTime earliestDue = earliestDueDate.atStartOfDay();

                        // Format due time
                        String dueTime = formatDueTime(earliestDue, LocalDateTime.now());

                        return DashboardResponse.UpcomingReview.builder()
                                .deckTitle(deck.getTitle())
                                .cardCount(cards.size())
                                .dueTime(dueTime)
                                .deckId(deck.getId())
                                .build();
                    })
                    .sorted((a, b) -> a.getDueTime().compareTo(b.getDueTime()))
                    .limit(5)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error getting upcoming reviews for user {}: {}", currentUser.getId(), e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Format due time in a human-readable format
     */
    private String formatDueTime(LocalDateTime dueTime, LocalDateTime now) {
        Duration duration = Duration.between(now, dueTime);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;

        if (hours < 1) {
            return minutes + " min";
        } else if (hours < 24) {
            return hours + "h " + minutes + "m";
        } else {
            return "Tomorrow";
        }
    }

    /**
     * Get recent decks for the user
     */
    private List<DashboardResponse.RecentDeck> getRecentDecks(User user) {
        try {
            // Get recently studied decks
            List<Deck> recentDecks = studySessionRepository.findRecentlyStudiedDecksForUser(
                    user.getId(),
                    LocalDateTime.now().minusDays(30),
                    PageRequest.of(0, 5)
            );

            return recentDecks.stream()
                    .map(deck -> DashboardResponse.RecentDeck.builder()
                            .id(deck.getId())
                            .deckId(deck.getId())
                            .title(deck.getTitle())
                            .cardCount(cardRepository.countByDeckId(deck.getId()))
                            .lastStudied(LocalDateTime.now().toString()) // This should be actual last studied time
                            .creatorUsername(deck.getCreator().getUsername())
                            .build())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error getting recent decks for user {}: {}", user.getId(), e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Format relative time for display
     */
    private String formatRelativeTime(LocalDateTime dateTime) {
        LocalDateTime now = LocalDateTime.now();
        long days = ChronoUnit.DAYS.between(dateTime, now);
        long hours = ChronoUnit.HOURS.between(dateTime, now);
        long minutes = ChronoUnit.MINUTES.between(dateTime, now);

        if (days > 0) {
            return days == 1 ? "1 day ago" : days + " days ago";
        } else if (hours > 0) {
            return hours == 1 ? "1 hour ago" : hours + " hours ago";
        } else if (minutes > 0) {
            return minutes == 1 ? "1 minute ago" : minutes + " minutes ago";
        } else {
            return "Just now";
        }
    }
}
