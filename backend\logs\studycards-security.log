2025-07-24 00:09:13.659 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-24 00:39:01.576 [http-nio-8082-exec-1] ERROR c.s.security.AuthEntryPointJwt - 
                [] [] [] [] Unauthorized error: Full authentication is required to access this resource
2025-07-24 00:39:27.287 [http-nio-8082-exec-4] INFO  c.s.security.CustomOAuth2UserService - 
                [] [] [] [] Google OAuth2 user attributes validated successfully for email: <EMAIL>
2025-07-24 00:39:27.293 [http-nio-8082-exec-4] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success handler called
2025-07-24 00:39:27.293 [http-nio-8082-exec-4] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Request URI: /login/oauth2/code/google
2025-07-24 00:39:27.293 [http-nio-8082-exec-4] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Query string: state=DDBGQ2XTeI075evi-yQs_DDVBAKguRfbPR-IxCkNcVs%3D&code=4%2F0AVMBsJjn0GSShWl_Lj7eXi9qUy_nFidcBcTnLp9c3zb2TMOsKBzpbosFwFsualDBAcsaoA&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile&authuser=1&prompt=none
2025-07-24 00:39:27.293 [http-nio-8082-exec-4] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success from Code
2025-07-24 00:39:27.373 [http-nio-8082-exec-4] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT token generated successfully for email: <EMAIL> (ID: f78b1374-69c2-4306-babd-804a9fccefe5)
2025-07-24 00:39:27.503 [http-nio-8082-exec-4] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 cookie configuration - requireHttps: false, isSecure: false, useSecureCookies: false
2025-07-24 00:39:27.504 [http-nio-8082-exec-4] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 access token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-24 00:39:27.504 [http-nio-8082-exec-4] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 refresh token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-24 01:21:12.079 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-24 01:21:12.086 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-24 01:21:12.086 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-24 01:21:12.086 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-24 01:21:12.086 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
