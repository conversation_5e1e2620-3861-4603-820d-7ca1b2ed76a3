package com.studycards.service;

import com.studycards.dto.DeckSearchRequest;
import com.studycards.dto.CardSearchRequest;
import com.studycards.dto.DeckResponse;
import com.studycards.dto.CardResponse;
import com.studycards.model.User;
import com.studycards.model.Deck;
import com.studycards.model.Card;
import com.studycards.repository.DeckRepository;
import com.studycards.repository.CardRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UnifiedSearchServiceTest {

    @Mock
    private DeckRepository deckRepository;

    @Mock
    private CardRepository cardRepository;

    @Mock
    private UserService userService;

    @Mock
    private DeckService deckService;

    @Mock
    private SearchAnalyticsService searchAnalyticsService;

    @InjectMocks
    private UnifiedSearchService unifiedSearchService;

    private User testUser;
    private Deck testDeck;
    private Card testCard;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");

        testDeck = new Deck();
        testDeck.setId(1L);
        testDeck.setTitle("Test Deck");
        testDeck.setDescription("Test Description");
        testDeck.setCreator(testUser);
        testDeck.setIsPublic(true);

        testCard = new Card();
        testCard.setId(1L);
        testCard.setQuestion("Test Question");
        testCard.setAnswer("Test Answer");
        testCard.setDeck(testDeck);
    }

    @Test
    void testSearchDecks_WithBasicQuery() {
        // Arrange
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("test")
            .page(0)
            .size(10)
            .build();

        List<Deck> deckList = Arrays.asList(testDeck);
        Page<Deck> deckPage = new PageImpl<>(deckList, PageRequest.of(0, 10), 1);
        
        DeckResponse deckResponse = DeckResponse.builder()
            .id(testDeck.getId())
            .title(testDeck.getTitle())
            .description(testDeck.getDescription())
            .build();

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(deckRepository.unifiedAdvancedSearch(
            eq("test"), isNull(), isNull(), isNull(), eq(true), 
            isNull(), isNull(), isNull(), isNull(), eq(false), 
            isNull(), isNull(), isNull(), isNull(), isNull(), 
            isNull(), isNull(), eq(false), eq(false), eq(1L), 
            any(Pageable.class)
        )).thenReturn(deckPage);
        when(deckService.mapToDeckResponse(eq(testDeck), eq(testUser))).thenReturn(deckResponse);

        // Act
        Page<DeckResponse> result = unifiedSearchService.searchDecks(request);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("Test Deck", result.getContent().get(0).getTitle());
        
        verify(searchAnalyticsService).trackSearch(eq(request), eq("deck"));
    }

    @Test
    void testSearchDecks_WithFilters() {
        // Arrange
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("test")
            .isPublic(true)
            .tagNames(Arrays.asList("java", "programming"))
            .minDifficulty(2)
            .maxDifficulty(4)
            .favoritesOnly(true)
            .page(0)
            .size(10)
            .build();

        List<Deck> deckList = Arrays.asList(testDeck);
        Page<Deck> deckPage = new PageImpl<>(deckList, PageRequest.of(0, 10), 1);
        
        DeckResponse deckResponse = DeckResponse.builder()
            .id(testDeck.getId())
            .title(testDeck.getTitle())
            .build();

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(deckRepository.unifiedAdvancedSearch(
            eq("test"), eq(true), isNull(), eq(Arrays.asList("java", "programming")), eq(false),
            eq(2), eq(4), isNull(), isNull(), eq(false), 
            isNull(), isNull(), isNull(), isNull(), isNull(), 
            isNull(), isNull(), eq(true), eq(false), eq(1L), 
            any(Pageable.class)
        )).thenReturn(deckPage);
        when(deckService.mapToDeckResponse(eq(testDeck), eq(testUser))).thenReturn(deckResponse);

        // Act
        Page<DeckResponse> result = unifiedSearchService.searchDecks(request);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(deckRepository).unifiedAdvancedSearch(
            eq("test"), eq(true), isNull(), eq(Arrays.asList("java", "programming")), eq(false),
            eq(2), eq(4), isNull(), isNull(), eq(false), 
            isNull(), isNull(), isNull(), isNull(), isNull(), 
            isNull(), isNull(), eq(true), eq(false), eq(1L), 
            any(Pageable.class)
        );
    }

    @Test
    void testSearchCards_WithBasicQuery() {
        // Arrange
        CardSearchRequest request = CardSearchRequest.builder()
            .query("test")
            .page(0)
            .size(10)
            .build();

        List<Card> cardList = Arrays.asList(testCard);
        Page<Card> cardPage = new PageImpl<>(cardList, PageRequest.of(0, 10), 1);

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(cardRepository.unifiedAdvancedSearch(
            eq("test"), isNull(), eq(true), isNull(), eq(true),
            isNull(), isNull(), isNull(), isNull(), isNull(), 
            isNull(), isNull(), isNull(), isNull(), isNull(), 
            eq(true), eq(true), eq(true), eq(false), eq(1L), 
            any(Pageable.class)
        )).thenReturn(cardPage);

        // Act
        Page<CardResponse> result = unifiedSearchService.searchCards(request);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        
        verify(searchAnalyticsService).trackSearch(eq(request), eq("card"));
    }

    @Test
    void testSearchDecks_EmptyQuery_NoResults() {
        // Arrange
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("")
            .page(0)
            .size(10)
            .build();

        Page<Deck> emptyPage = new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 10), 0);

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(deckRepository.unifiedAdvancedSearch(
            eq(""), isNull(), isNull(), isNull(), eq(true), 
            isNull(), isNull(), isNull(), isNull(), eq(false), 
            isNull(), isNull(), isNull(), isNull(), isNull(), 
            isNull(), isNull(), eq(false), eq(false), eq(1L), 
            any(Pageable.class)
        )).thenReturn(emptyPage);

        // Act
        Page<DeckResponse> result = unifiedSearchService.searchDecks(request);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testSearchDecks_WithMaxResultsLimit() {
        // Arrange
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("test")
            .maxResults(5000) // Above the default limit
            .page(0)
            .size(10)
            .build();

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(deckRepository.unifiedAdvancedSearch(any(), any(), any(), any(), any(), 
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), 
            any(), any(), any(), any(), any(), any(Pageable.class)))
            .thenReturn(new PageImpl<>(Collections.emptyList()));

        // Act
        unifiedSearchService.searchDecks(request);

        // Assert - maxResults should be capped at the service limit (1000)
        assertEquals(1000, request.getMaxResults());
    }

    @Test
    void testSearchDecks_QuerySanitization() {
        // Arrange
        String longQuery = "a".repeat(600); // Longer than 500 chars
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query(longQuery)
            .page(0)
            .size(10)
            .build();

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(deckRepository.unifiedAdvancedSearch(any(), any(), any(), any(), any(), 
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), 
            any(), any(), any(), any(), any(), any(Pageable.class)))
            .thenReturn(new PageImpl<>(Collections.emptyList()));

        // Act
        unifiedSearchService.searchDecks(request);

        // Assert - query should be truncated to 500 chars
        assertEquals(500, request.getQuery().length());
    }

    @Test
    void testSearchDecks_RelevanceRanking() {
        // Arrange
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("test")
            .enableRelevanceRanking(true)
            .rankingStrategy("relevance")
            .page(0)
            .size(10)
            .build();

        List<Deck> deckList = Arrays.asList(testDeck);
        Page<Deck> deckPage = new PageImpl<>(deckList, PageRequest.of(0, 10), 1);
        
        DeckResponse deckResponse = DeckResponse.builder()
            .id(testDeck.getId())
            .title(testDeck.getTitle())
            .build();

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(deckRepository.unifiedAdvancedSearch(any(), any(), any(), any(), any(), 
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), 
            any(), any(), any(), any(), any(), any(Pageable.class)))
            .thenReturn(deckPage);
        when(deckService.mapToDeckResponse(eq(testDeck), eq(testUser))).thenReturn(deckResponse);

        // Act
        Page<DeckResponse> result = unifiedSearchService.searchDecks(request);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        
        // Verify that relevance ranking was considered in pageable creation
        verify(deckRepository).unifiedAdvancedSearch(any(), any(), any(), any(), any(),
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any(),
            any(), any(), any(), any(), any(), argThat(pageable ->
                pageable.getSort().toString().contains("createdAt")
            ));
    }

    @Test
    void testSearchDecks_DateFilters() {
        // Arrange
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);
        
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("test")
            .createdAfter(startDate)
            .createdBefore(endDate)
            .page(0)
            .size(10)
            .build();

        when(userService.getCurrentUser()).thenReturn(testUser);
        when(deckRepository.unifiedAdvancedSearch(
            eq("test"), isNull(), isNull(), isNull(), eq(true), 
            isNull(), isNull(), isNull(), isNull(), eq(false), 
            isNull(), isNull(), isNull(), eq(startDate), eq(endDate), 
            isNull(), isNull(), eq(false), eq(false), eq(1L), 
            any(Pageable.class)
        )).thenReturn(new PageImpl<>(Collections.emptyList()));

        // Act
        unifiedSearchService.searchDecks(request);

        // Assert
        verify(deckRepository).unifiedAdvancedSearch(
            eq("test"), isNull(), isNull(), isNull(), eq(true), 
            isNull(), isNull(), isNull(), isNull(), eq(false), 
            isNull(), isNull(), isNull(), eq(startDate), eq(endDate), 
            isNull(), isNull(), eq(false), eq(false), eq(1L), 
            any(Pageable.class)
        );
    }
}
