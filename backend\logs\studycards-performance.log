2025-07-24 00:09:26.450 [MessageBroker-10] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [5ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 5ms with error: NullPointerException
2025-07-24 00:09:26.768 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [245ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 245ms
2025-07-24 00:09:26.768 [MessageBroker-7] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [245ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 245ms
2025-07-24 00:09:26.769 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [208ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 208ms
2025-07-24 00:09:26.769 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [209ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 209ms
2025-07-24 00:09:26.776 [main] INFO  PERFORMANCE - 
                [] [119ms] [] DB_OPERATION: $Proxy211.count took 119ms
2025-07-24 00:09:27.064 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [517ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 517ms
2025-07-24 00:09:27.064 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [517ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 517ms
2025-07-24 00:09:27.066 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [624ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 624ms
2025-07-24 00:09:27.066 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [628ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 628ms
2025-07-24 00:39:29.853 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getStudyActivityData] [122ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 122ms
2025-07-24 00:39:29.866 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getUserStudySessions] [107ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 107ms
2025-07-24 00:39:29.888 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getUserDecks] [103ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 103ms
2025-07-24 00:39:29.911 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [110ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 110ms
2025-07-24 00:39:30.039 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getDashboardData] [71ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 71ms with error: ConversionFailedException
2025-07-24 00:39:30.297 [StudyCards-Async-2] INFO  PERFORMANCE - 
                [createDueCardsReminder] [142ms] [] DB_OPERATION: $Proxy220.save took 142ms
2025-07-24 00:40:03.206 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getDashboardData] [20ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 20ms with error: ConversionFailedException
2025-07-24 00:41:00.455 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [mapToDeckResponse] [15ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 15ms with error: ServiceException
2025-07-24 00:41:00.458 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [37ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 37ms with error: ServiceException
2025-07-24 00:41:40.788 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [mapToDeckResponse] [11ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 11ms with error: ServiceException
2025-07-24 00:41:40.789 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [24ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 24ms with error: ServiceException
2025-07-24 00:41:59.793 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [mapToDeckResponse] [11ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 11ms with error: ServiceException
2025-07-24 00:41:59.795 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [21ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 21ms with error: ServiceException
2025-07-24 00:42:26.795 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [mapToDeckResponse] [17ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 17ms with error: ServiceException
2025-07-24 00:42:26.796 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [31ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 31ms with error: ServiceException
2025-07-24 00:44:00.186 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [1ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 1ms with error: NullPointerException
2025-07-24 00:44:04.874 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:08.581 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [1ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 1ms with error: NullPointerException
2025-07-24 00:44:16.998 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:18.854 [StudyCards-Async-3] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:35.642 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:35.679 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:36.317 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:36.451 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:44.952 [StudyCards-Async-3] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:53.502 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:00.670 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:18.377 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:22.950 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:23.558 [StudyCards-Async-3] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:23.971 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:26.006 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [2ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 2ms with error: NullPointerException
2025-07-24 00:45:26.265 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:26.679 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:46:12.350 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [565ms] [] SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 565ms
2025-07-24 01:21:26.250 [MessageBroker-9] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-24 01:21:26.558 [MessageBroker-13] INFO  PERFORMANCE - 
                [sendVerificationReminders] [250ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 250ms
2025-07-24 01:21:26.558 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [250ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 250ms
2025-07-24 01:21:26.558 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [230ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 230ms
2025-07-24 01:21:26.558 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [228ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 228ms
2025-07-24 01:21:26.565 [main] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy211.count took 162ms
2025-07-24 01:21:26.780 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [137ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 137ms
2025-07-24 01:21:26.781 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [140ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 140ms
2025-07-24 01:21:26.942 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [628ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 628ms
2025-07-24 01:21:26.942 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [628ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 628ms
2025-07-24 01:21:26.944 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [695ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 695ms
2025-07-24 01:21:26.945 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [696ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 696ms
2025-07-24 01:22:03.268 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [135ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 135ms
2025-07-24 01:22:03.270 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getDashboardData] [102ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 102ms with error: ConversionFailedException
2025-07-24 01:22:03.502 [StudyCards-Async-2] INFO  PERFORMANCE - 
                [createDueCardsReminder] [167ms] [] DB_OPERATION: $Proxy220.save took 167ms
2025-07-24 01:22:12.066 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getDashboardData] [7ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 7ms with error: ConversionFailedException
2025-07-24 01:22:47.649 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getCurrentUser] [148ms] [] DB_OPERATION: $Proxy194.findById took 148ms
2025-07-24 01:22:48.153 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [303ms] [] DB_OPERATION: $Proxy194.findById took 303ms
2025-07-24 01:22:50.186 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [6ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 6ms with error: NullPointerException
2025-07-24 01:22:50.616 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [searchDecks] [458ms] [] DB_OPERATION: $Proxy206.unifiedAdvancedSearch took 458ms
2025-07-24 01:22:50.715 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [searchDecks] [592ms] [] SERVICE_METHOD: UnifiedSearchService.searchDecks took 592ms
2025-07-24 01:23:03.136 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 01:23:03.165 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [29ms] [] FAILED_DB_OPERATION: $Proxy206.unifiedAdvancedSearch failed after 29ms with error: InvalidDataAccessApiUsageException
2025-07-24 01:23:03.166 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [32ms] [] FAILED_SERVICE_METHOD: UnifiedSearchService.searchDecks failed after 32ms with error: InvalidDataAccessApiUsageException
2025-07-24 01:23:03.171 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [37ms] [] FAILED_ENDPOINT: UnifiedSearchController.searchDecks failed after 37ms with error: InvalidDataAccessApiUsageException
2025-07-24 01:24:57.383 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getDashboardData] [9ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 9ms with error: ConversionFailedException
2025-07-24 02:00:00.040 [MessageBroker-1] ERROR PERFORMANCE - 
                [cleanupOldSearchHistory] [11ms] [] FAILED_DB_OPERATION: $Proxy243.deleteOldSearchHistory failed after 11ms with error: InvalidDataAccessApiUsageException
2025-07-24 02:01:26.645 [ForkJoinPool.commonPool-worker-12] INFO  PERFORMANCE - 
                [] [180ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 180ms
2025-07-24 02:01:26.792 [MessageBroker-5] INFO  PERFORMANCE - 
                [periodicHealthCheck] [535ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 535ms
2025-07-24 02:01:26.792 [MessageBroker-5] INFO  PERFORMANCE - 
                [periodicHealthCheck] [551ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 551ms
2025-07-24 11:14:13.180 [MessageBroker-9] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [240ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 240ms
2025-07-24 11:14:13.292 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [120ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 120ms
2025-07-24 11:14:13.395 [MessageBroker-2] WARN  PERFORMANCE - 
                [generateDailyNotifications] [514ms] [] SLOW_DB_OPERATION: $Proxy194.findAll took 514ms
2025-07-24 11:14:13.395 [MessageBroker-2] INFO  PERFORMANCE - 
                [generateDailyNotifications] [545ms] [] SERVICE_METHOD: UserService.getAllUsers took 545ms
2025-07-24 11:14:13.543 [MessageBroker-2] ERROR PERFORMANCE - 
                [generateDailyNotifications] [74ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 74ms with error: ConversionFailedException
2025-07-24 11:14:13.627 [MessageBroker-2] INFO  PERFORMANCE - 
                [generateDailyNotifications] [779ms] [] SERVICE_METHOD: DashboardService.generateDailyNotifications took 779ms
2025-07-24 11:14:13.685 [MessageBroker-7] INFO  PERFORMANCE - 
                [sendVerificationReminders] [459ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 459ms
2025-07-24 11:14:13.722 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [366ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 366ms
2025-07-24 11:14:13.822 [MessageBroker-7] INFO  PERFORMANCE - 
                [sendVerificationReminders] [137ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 137ms
2025-07-24 11:14:13.823 [MessageBroker-7] INFO  PERFORMANCE - 
                [sendVerificationReminders] [615ms] [] SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 615ms
2025-07-24 11:14:13.869 [ForkJoinPool.commonPool-worker-15] INFO  PERFORMANCE - 
                [] [197ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 197ms
2025-07-24 11:14:13.896 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [174ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 174ms
2025-07-24 11:14:14.575 [MessageBroker-6] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [1384ms] [] SLOW_SERVICE_METHOD: CacheUtilityService.silentClear took 1384ms
2025-07-24 11:14:14.578 [MessageBroker-6] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [1400ms] [] SLOW_SERVICE_METHOD: SearchCacheService.invalidateSearchCaches took 1400ms
2025-07-24 11:14:14.596 [ForkJoinPool.commonPool-worker-14] WARN  PERFORMANCE - 
                [] [608ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 608ms
2025-07-24 11:14:14.660 [ForkJoinPool.commonPool-worker-15] WARN  PERFORMANCE - 
                [] [672ms] [] SLOW_DB_OPERATION: $Proxy208.countDistinctTags took 672ms
2025-07-24 11:14:14.690 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [1830ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1830ms
2025-07-24 11:14:14.690 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [1846ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 1846ms
2025-07-24 11:14:14.837 [MessageBroker-1] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1977ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1977ms
2025-07-24 11:14:14.841 [MessageBroker-1] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2000ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 2000ms
2025-07-24 11:14:15.082 [ForkJoinPool.commonPool-worker-15] INFO  PERFORMANCE - 
                [] [182ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 182ms
2025-07-24 11:14:15.082 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [155ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 155ms
2025-07-24 11:14:15.235 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [114ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 114ms
2025-07-24 11:14:15.251 [ForkJoinPool.commonPool-worker-15] INFO  PERFORMANCE - 
                [] [113ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 113ms
2025-07-24 11:14:15.279 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [589ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 589ms
2025-07-24 11:14:15.282 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [592ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 592ms
2025-07-24 11:14:15.427 [MessageBroker-1] INFO  PERFORMANCE - 
                [periodicHealthCheck] [580ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 580ms
2025-07-24 11:14:15.428 [MessageBroker-1] INFO  PERFORMANCE - 
                [periodicHealthCheck] [587ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 587ms
2025-07-24 11:14:16.594 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [102ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 102ms
2025-07-24 11:14:16.808 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 162ms
2025-07-24 11:14:16.826 [MessageBroker-4] INFO  PERFORMANCE - 
                [cacheWarming] [528ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 528ms
2025-07-24 11:14:16.828 [MessageBroker-4] INFO  PERFORMANCE - 
                [cacheWarming] [532ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 532ms
